# -*- coding: utf-8 -*-
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# Copyright (c) 2021-present Kaleidos INC
#
# The code is partially taken (and modified) from djangorestframework-simplejwt v. 4.7.1
# (https://github.com/jazzband/djangorestframework-simplejwt/tree/5997c1aee8ad5182833d6b6759e44ff0a704edb4)
# that is licensed under the following terms:
#
#   Copyright 2017 <PERSON>
#
#   Permission is hereby granted, free of charge, to any person obtaining a copy of
#   this software and associated documentation files (the "Software"), to deal in
#   the Software without restriction, including without limitation the rights to
#   use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies
#   of the Software, and to permit persons to whom the Software is furnished to do
#   so, subject to the following conditions:
#
#   The above copyright notice and this permission notice shall be included in all
#   copies or substantial portions of the Software.
#
#   THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
#   IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
#   FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
#   AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
#   LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
#   OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
#   SOFTWARE.

from datetime import datetime, timedelta
import pytest
from unittest.mock import patch

import jwt
from jwt import PyJWS, algorithms

from taiga.auth.backends import TokenBackend
from taiga.auth.exceptions import TokenBackendError
from taiga.auth.utils import (
    aware_utcnow, datetime_to_epoch, make_utc,
)

SECRET = 'not_secret'

PRIVATE_KEY = '''
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'''

PUBLIC_KEY = '''
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA3xMJfyl8TOdrsjDLSIod
sArJ/NnQB3ZdfbFC5onxATDfRLLACHFo3ye694doBKeSe1NFYbfXPvahl6ODX1a2
3oQyoRQwlL+M99cLcdCa0gGuJXdbAaF6Em8E+7uSb3290mI+rZmjqyc7gMtKVWKL
4e5i2PerFFBoYkZ7E90KOp2t0ZADx2uqF4VTOfYLHG0cPgSw9/ptDStJqJVAOiRR
qbv0j0GOFMDYNcN0mDlnpryhQFbQiMqn4IJIURZUVBJujFSa45cJPvSmMb6NrzZ1
crg5UN6/5Mu2mxQzAi21+vpgGL+EEuekUd7sRgEAjTHjLKzotLAGo7EGa8sL1vMS
FwIDAQAB
-----END PUBLIC KEY-----
'''

AUDIENCE = 'openid-client-id'

ISSUER = 'https://www.myoidcprovider.com'


hmac_token_backend = TokenBackend('HS256', SECRET)
rsa_token_backend = TokenBackend('RS256', PRIVATE_KEY, PUBLIC_KEY)
aud_iss_token_backend = TokenBackend('RS256', PRIVATE_KEY, PUBLIC_KEY, AUDIENCE, ISSUER)
payload = {'foo': 'bar'}


def test_init():
    # Should reject unknown algorithms
    with pytest.raises(TokenBackendError):
        TokenBackend('oienarst oieanrsto i', 'not_secret')

    TokenBackend('HS256', 'not_secret')


@patch.object(algorithms, 'has_crypto', new=False)
def test_init_fails_for_rs_algorithms_when_crypto_not_installed():
    with pytest.raises(TokenBackendError, match=r'You must have cryptography installed to use RS256.'):
        TokenBackend('RS256', 'not_secret')
    with pytest.raises(TokenBackendError, match=r'You must have cryptography installed to use RS384.'):
        TokenBackend('RS384', 'not_secret')
    with pytest.raises(TokenBackendError, match=r'You must have cryptography installed to use RS512.'):
        TokenBackend('RS512', 'not_secret')


def test_encode_hmac():
    # Should return a JSON web token for the given payload
    payload = {'exp': make_utc(datetime(year=2000, month=1, day=1))}

    hmac_token = hmac_token_backend.encode(payload)

    # Token could be one of two depending on header dict ordering
    assert hmac_token in (
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJleHAiOjk0NjY4NDgwMH0.NHpdD2X8ub4SE_MZLBedWa57FCpntGaN_r6f8kNKdUs',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk0NjY4NDgwMH0.jvxQgXCSDToR8uKoRJcMT-LmMJJn2-NM76nfSR2FOgs',
    )


def test_encode_rsa():
    # Should return a JSON web token for the given payload
    payload = {'exp': make_utc(datetime(year=2000, month=1, day=1))}

    rsa_token = rsa_token_backend.encode(payload)

    # Token could be one of two depending on header dict ordering
    assert rsa_token in (
        '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    )


def test_encode_aud_iss():
    # Should return a JSON web token for the given payload
    original_payload = {'exp': make_utc(datetime(year=2000, month=1, day=1))}
    payload = original_payload.copy()

    rsa_token = aud_iss_token_backend.encode(payload)

    # Assert that payload has not been mutated by the encode() function
    assert payload == original_payload

    # Token could be one of 12 depending on header dict ordering
    assert rsa_token in (
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJodHRwczovL3d3dy5teW9pZGNwcm92aWRlci5jb20iLCJleHAiOjk0NjY4NDgwMCwiYXVkIjoib3BlbmlkLWNsaWVudC1pZCJ9.b4pdohov81oqzLyCIp4y7e4VYz7LSez7bH0t1o0Zwzau1uXPYXcasT9lxxNMEEiZwHIovPLyWQ6XvF0bMWTk9vc4PyIpkLqsLBJPsuQ-wYUOD04fECmqUX_JaINqm2pPbohTcOQwl0xsE1UMIKTFBZDL1hEXGEMdW9lrPcXilhbC1ikyMpzsmVh55Q_wL2GqydssnOOcDQTqEkWoKvELJJhBcE-YuQkUp8jEVhF3VZ4jEZkzCErTlyXcfe1qXZHkWtw2QNo9s_SfLuRy_fACOo8XE9pHBoE7rqiSm-FmISgiLO1Jj3Pqq-abjN4SnAbU7PZWcV3fUoO1eYLGORmAcw',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    )


def test_decode_hmac_with_no_expiry():
    no_exp_token = jwt.encode(payload, SECRET, algorithm='HS256')

    hmac_token_backend.decode(no_exp_token)


def test_decode_hmac_with_no_expiry_no_verify():
    no_exp_token = jwt.encode(payload, SECRET, algorithm='HS256')

    assert hmac_token_backend.decode(no_exp_token, verify=False) == payload


def test_decode_hmac_with_expiry():
    payload['exp'] = aware_utcnow() - timedelta(seconds=1)

    expired_token = jwt.encode(payload, SECRET, algorithm='HS256')

    with pytest.raises(TokenBackendError):
        hmac_token_backend.decode(expired_token)


def test_decode_hmac_with_invalid_sig():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    token_1 = jwt.encode(payload, SECRET, algorithm='HS256')
    payload['foo'] = 'baz'
    token_2 = jwt.encode(payload, SECRET, algorithm='HS256')

    token_2_payload = token_2.rsplit('.', 1)[0]
    token_1_sig = token_1.rsplit('.', 1)[-1]
    invalid_token = token_2_payload + '.' + token_1_sig

    with pytest.raises(TokenBackendError):
        hmac_token_backend.decode(invalid_token)


def test_decode_hmac_with_invalid_sig_no_verify():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    token_1 = jwt.encode(payload, SECRET, algorithm='HS256')
    payload['foo'] = 'baz'
    token_2 = jwt.encode(payload, SECRET, algorithm='HS256')
    # Payload copied
    payload["exp"] = datetime_to_epoch(payload["exp"])

    token_2_payload = token_2.rsplit('.', 1)[0]
    token_1_sig = token_1.rsplit('.', 1)[-1]
    invalid_token = token_2_payload + '.' + token_1_sig

    assert hmac_token_backend.decode(invalid_token, verify=False) == payload


def test_decode_hmac_success():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    payload['foo'] = 'baz'

    token = jwt.encode(payload, SECRET, algorithm='HS256')
    # Payload copied
    payload["exp"] = datetime_to_epoch(payload["exp"])

    assert hmac_token_backend.decode(token) == payload


def test_decode_rsa_with_no_expiry():
    no_exp_token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    rsa_token_backend.decode(no_exp_token)


def test_decode_rsa_with_no_expiry_no_verify():
    no_exp_token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    assert hmac_token_backend.decode(no_exp_token, verify=False) == payload


def test_decode_rsa_with_expiry():
    payload['exp'] = aware_utcnow() - timedelta(seconds=1)

    expired_token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    with pytest.raises(TokenBackendError):
        rsa_token_backend.decode(expired_token)


def test_decode_rsa_with_invalid_sig():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    payload['foo'] = 'baz'
    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    token_payload = token.rsplit('.', 1)[0]
    token_sig = token.rsplit('.', 1)[-1]
    invalid_token = token_payload + '.' + token_sig.replace("a", "A")

    with pytest.raises(TokenBackendError):
        rsa_token_backend.decode(invalid_token)


def test_decode_rsa_with_invalid_sig_no_verify():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    payload['foo'] = 'baz'
    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    token_payload = token.rsplit('.', 1)[0]
    token_sig = token.rsplit('.', 1)[-1]
    invalid_token = token_payload + '.' + token_sig.replace("a", "A")

    # Payload copied
    payload["exp"] = datetime_to_epoch(payload["exp"])

    assert hmac_token_backend.decode(invalid_token, verify=False) == payload


def test_decode_rsa_success():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    payload['foo'] = 'baz'

    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')
    # Payload copied
    payload["exp"] = datetime_to_epoch(payload["exp"])

    assert rsa_token_backend.decode(token) == payload


def test_decode_aud_iss_success():
    payload['exp'] = aware_utcnow() + timedelta(days=1)
    payload['foo'] = 'baz'
    payload['aud'] = AUDIENCE
    payload['iss'] = ISSUER

    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')
    # Payload copied
    payload["exp"] = datetime_to_epoch(payload["exp"])

    assert aud_iss_token_backend.decode(token) == payload


def test_decode_when_algorithm_not_available():
    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    pyjwt_without_rsa = PyJWS()
    pyjwt_without_rsa.unregister_algorithm('RS256')
    with patch.object(jwt, 'decode', new=pyjwt_without_rsa.decode):
        with pytest.raises(TokenBackendError, match=r'Invalid algorithm specified'):
            rsa_token_backend.decode(token)


def test_decode_when_token_algorithm_does_not_match():
    token = jwt.encode(payload, PRIVATE_KEY, algorithm='RS256')

    with pytest.raises(TokenBackendError, match=r'Invalid algorithm specified'):
        hmac_token_backend.decode(token)
