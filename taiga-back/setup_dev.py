#!/usr/bin/env python3
"""
Taiga Backend Development Setup Script

This script helps set up the Taiga backend for development purposes.
Note: Taiga is designed to work with PostgreSQL, but this setup uses SQLite for simplicity.
Some features may not work properly with SQLite.

For production use, please refer to the official Taiga documentation.
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return None

def create_simple_database():
    """Create a simple SQLite database with basic tables."""
    print("\n🔄 Creating simple database...")
    
    db_path = "db.sqlite3"
    if os.path.exists(db_path):
        os.remove(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create basic user table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users_user (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(255) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            first_name VARCHAR(255),
            last_name VARCHAR(255),
            is_active BOOLEAN DEFAULT 1,
            is_staff BOOLEAN DEFAULT 0,
            is_superuser BOOLEAN DEFAULT 0,
            date_joined DATETIME DEFAULT CURRENT_TIMESTAMP,
            password VARCHAR(255) NOT NULL
        )
    ''')
    
    # Create basic session table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS django_session (
            session_key VARCHAR(40) PRIMARY KEY,
            session_data TEXT NOT NULL,
            expire_date DATETIME NOT NULL
        )
    ''')
    
    conn.commit()
    conn.close()
    print("✅ Simple database created")

def main():
    print("🚀 Taiga Backend Development Setup")
    print("=" * 50)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected.")
        print("Please activate the virtual environment first:")
        print("source venv/bin/activate")
        return
    
    # Check if we're in the right directory
    if not os.path.exists("manage.py"):
        print("❌ Error: manage.py not found. Please run this script from the taiga-back directory.")
        return
    
    # Check if dependencies are installed
    try:
        import django
        print(f"✅ Django {django.get_version()} is installed")
    except ImportError:
        print("❌ Django not found. Please install requirements first:")
        print("pip install -r requirements.txt")
        return
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings.config')
    
    # Create simple database
    create_simple_database()
    
    # Try to run basic Django commands
    run_command("python manage.py check", "Running Django system check")
    
    print("\n🎉 Basic setup completed!")
    print("\n📋 Next steps:")
    print("1. To start the development server:")
    print("   DJANGO_SETTINGS_MODULE=settings.config python manage.py runserver")
    print("\n2. To create a superuser (may not work with simple setup):")
    print("   DJANGO_SETTINGS_MODULE=settings.config python manage.py createsuperuser")
    print("\n⚠️  Note: This is a simplified setup for development.")
    print("   For full functionality, please set up PostgreSQL and run proper migrations.")
    print("   Refer to the official Taiga documentation for production setup.")

if __name__ == "__main__":
    main()
