# Taiga Backend Setup Guide

## Overview

This guide documents the setup of the Taiga backend Django application. Taiga is a project management platform built with Django and designed to work with PostgreSQL.

## What Has Been Completed

### ✅ Repository Setup
- Cloned the official Taiga backend repository from https://github.com/taigaio/taiga-back
- Repository is located at `/home/<USER>/Desktop/ProjectX/taiga-back`

### ✅ Python Environment
- Created Python virtual environment (`venv/`)
- Installed all required dependencies from `requirements.txt`
- Modified `psycopg2` to `psycopg2-binary` for easier installation
- Python 3.8.10 is being used (compatible with requirements)

### ✅ Django Configuration
- Created development configuration file: `settings/config.py`
- Configured SQLite database for development (note: Taiga is designed for PostgreSQL)
- Set up basic Django settings for development environment
- Django system checks pass successfully

### ✅ Development Tools
- Created `setup_dev.py` script for easy development setup
- Created basic SQLite database with minimal tables

## Current Status

The Taiga backend is **partially set up** for development. The main limitation is that Taiga's migrations are PostgreSQL-specific and don't work with SQLite.

### What Works:
- Django configuration loads successfully
- Virtual environment with all dependencies
- Basic Django system checks pass
- Development configuration is ready

### What Needs PostgreSQL:
- Database migrations (contain PostgreSQL-specific code)
- Full application functionality
- Production-ready setup

## Next Steps

### Option 1: Full PostgreSQL Setup (Recommended)
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres createdb taiga
sudo -u postgres createuser taiga
sudo -u postgres psql -c "ALTER USER taiga WITH PASSWORD 'taiga';"
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE taiga TO taiga;"

# Update settings to use PostgreSQL
# Edit settings/config.py to use PostgreSQL instead of SQLite

# Run migrations
source venv/bin/activate
DJANGO_SETTINGS_MODULE=settings.config python manage.py migrate

# Create superuser
DJANGO_SETTINGS_MODULE=settings.config python manage.py createsuperuser

# Start server
DJANGO_SETTINGS_MODULE=settings.config python manage.py runserver 0.0.0.0:8000
```

### Option 2: Docker Setup (Alternative)
Use the existing `taiga-docker` setup in the parent directory for a complete containerized environment.

### Option 3: Development with Limitations
For basic Django development and testing (with limited functionality):
```bash
source venv/bin/activate
python setup_dev.py
# Note: Server may not start due to migration issues
```

## File Structure

```
taiga-back/
├── venv/                    # Python virtual environment
├── settings/
│   ├── config.py           # Development configuration
│   ├── config.py.dev.example
│   └── common.py           # Base settings
├── taiga/                  # Main application code
├── requirements.txt        # Python dependencies (modified)
├── setup_dev.py           # Development setup script
├── manage.py              # Django management script
└── README_SETUP.md        # This file
```

## Important Notes

1. **PostgreSQL Dependency**: Taiga is specifically designed for PostgreSQL and many features rely on PostgreSQL-specific functionality.

2. **Migration Issues**: The current SQLite setup cannot run migrations due to PostgreSQL-specific code in migration files.

3. **Production Use**: For production deployment, always use PostgreSQL and follow the official Taiga documentation.

4. **Development**: For full development capabilities, PostgreSQL setup is strongly recommended.

## Commands Reference

```bash
# Activate virtual environment
source venv/bin/activate

# Run Django checks
DJANGO_SETTINGS_MODULE=settings.config python manage.py check

# Start development server (requires PostgreSQL for full functionality)
DJANGO_SETTINGS_MODULE=settings.config python manage.py runserver 0.0.0.0:8000

# Run setup script
python setup_dev.py
```

## Troubleshooting

- **Migration errors**: Install and configure PostgreSQL
- **Import errors**: Ensure virtual environment is activated
- **Permission errors**: Check file permissions and virtual environment setup
- **Database connection errors**: Verify PostgreSQL is running and configured correctly

For full functionality and production use, please refer to the official Taiga documentation at https://docs.taiga.io/
