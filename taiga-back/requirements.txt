#
# This file is autogenerated by pip-compile with Python 3.8
# by the following command:
#
#    pip-compile requirements.in
#
aggdraw==1.3.15
    # via psd-tools
amqp==5.1.1
    # via kombu
asana==3.1.0
    # via -r requirements.in
asgiref==3.6.0
    # via django
async-timeout==4.0.2
    # via redis
attrs==22.2.0
    # via psd-tools
backoff==1.6.0
    # via rudder-sdk-python
billiard==*******
    # via celery
bleach==4.1.0
    # via -r requirements.in
cairocffi==1.4.0
    # via cairosvg
cairosvg==2.6.0
    # via -r requirements.in
celery==5.2.7
    # via -r requirements.in
certifi==2022.12.7
    # via requests
cffi==1.15.1
    # via
    #   cairocffi
    #   cryptography
charset-normalizer==2.0.12
    # via requests
click==8.1.3
    # via
    #   celery
    #   click-didyoumean
    #   click-plugins
    #   click-repl
click-didyoumean==0.3.0
    # via celery
click-plugins==1.1.1
    # via celery
click-repl==0.2.0
    # via celery
cryptography==39.0.1
    # via oauthlib
cssselect==1.2.0
    # via premailer
cssselect2==0.7.0
    # via cairosvg
cssutils==2.6.0
    # via premailer
defusedxml==0.7.1
    # via cairosvg
diff-match-patch==20121119
    # via -r requirements.in
django==3.2.19
    # via
    #   -r requirements.in
    #   django-jinja
    #   django-picklefield
    #   django-sampledatahelper
    #   django-sites
    #   django-sr
    #   easy-thumbnails
django-ipware==1.1.6
    # via -r requirements.in
django-jinja==2.10.2
    # via -r requirements.in
django-pglocks==1.0.4
    # via -r requirements.in
django-picklefield==3.1
    # via -r requirements.in
django-sampledatahelper==0.4.1
    # via -r requirements.in
django-sites==0.11
    # via -r requirements.in
django-sr==0.0.4
    # via -r requirements.in
djmail==2.0.0
    # via -r requirements.in
docopt==0.6.2
    # via psd-tools
easy-thumbnails==2.8.5
    # via -r requirements.in
gunicorn==20.1.0
    # via -r requirements.in
html5lib==1.1
    # via -r requirements.in
idna==3.4
    # via requests
imageio==2.25.1
    # via scikit-image
importlib-metadata==6.0.0
    # via markdown
jinja2==3.1.2
    # via django-jinja
kombu==5.2.4
    # via celery
lxml==4.9.2
    # via premailer
markdown==3.4.1
    # via
    #   -r requirements.in
    #   pymdown-extensions
markupsafe==2.1.2
    # via jinja2
monotonic==1.6
    # via rudder-sdk-python
netaddr==0.8.0
    # via -r requirements.in
networkx==3.0
    # via scikit-image
numpy==1.24.2
    # via
    #   imageio
    #   psd-tools
    #   pywavelets
    #   scikit-image
    #   scipy
    #   tifffile
oauthlib[signedtoken]==3.2.2
    # via
    #   -r requirements.in
    #   requests-oauthlib
packaging==23.0
    # via
    #   bleach
    #   scikit-image
pillow==9.4.0
    # via
    #   -r requirements.in
    #   cairosvg
    #   easy-thumbnails
    #   imageio
    #   psd-tools
    #   scikit-image
premailer==3.0.1
    # via -r requirements.in
prompt-toolkit==3.0.36
    # via click-repl
psd-tools==1.9.18
    # via -r requirements.in
psycopg2-binary==2.9.10
    # via -r requirements.in
pycparser==2.21
    # via cffi
pygments==2.14.0
    # via -r requirements.in
pyjwt==2.6.0
    # via oauthlib
pymdown-extensions==9.9.2
    # via -r requirements.in
python-dateutil==2.7.5
    # via
    #   -r requirements.in
    #   rudder-sdk-python
python-magic==0.4.15
    # via -r requirements.in
pytz==2022.7.1
    # via
    #   -r requirements.in
    #   celery
    #   django
    #   sampledata
pywavelets==1.4.1
    # via scikit-image
raven==6.10.0
    # via -r requirements.in
redis==4.5.3
    # via -r requirements.in
requests==2.27.1
    # via
    #   -r requirements.in
    #   asana
    #   premailer
    #   requests-oauthlib
    #   rudder-sdk-python
requests-oauthlib==1.3.1
    # via
    #   -r requirements.in
    #   asana
rudder-sdk-python==1.0.0b1
    # via -r requirements.in
sampledata==0.3.7
    # via django-sampledatahelper
scikit-image==0.19.3
    # via psd-tools
scipy==1.10.0
    # via
    #   psd-tools
    #   scikit-image
serpy==0.1.1
    # via -r requirements.in
six==1.16.0
    # via
    #   bleach
    #   click-repl
    #   django-pglocks
    #   django-sampledatahelper
    #   html5lib
    #   python-dateutil
    #   rudder-sdk-python
    #   sampledata
    #   serpy
    #   webcolors
sqlparse==0.4.3
    # via django
tifffile==2023.2.3
    # via scikit-image
tinycss2==1.2.1
    # via
    #   cairosvg
    #   cssselect2
unidecode==0.4.20
    # via -r requirements.in
urllib3==1.26.14
    # via requests
vine==5.0.0
    # via
    #   amqp
    #   celery
    #   kombu
wcwidth==0.2.6
    # via prompt-toolkit
webcolors==1.9.1
    # via -r requirements.in
webencodings==0.5.1
    # via
    #   bleach
    #   cssselect2
    #   html5lib
    #   tinycss2
zipp==1.2.0
    # via
    #   -r requirements.in
    #   importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
