# -*- coding: utf-8 -*-
from .common import *  # noqa, pylint: disable=unused-wildcard-import

DEBUG = True

TEMPLATES[0]["OPTIONS"]['context_processors'] += "django.template.context_processors.debug"

ENABLE_TELEMETRY = False

# Database configuration for development
# Note: Taiga is designed for PostgreSQL, but for development we'll try SQLite
# Some features may not work properly with SQLite
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
    }
}

# Remove PostgreSQL-specific apps that might cause issues
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != "django.contrib.postgres"]

# Media files configuration
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Static files configuration
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'static')

# Email configuration for development (console backend)
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Redis configuration for development (optional)
REDIS_URL = 'redis://localhost:6379/0'

# Celery configuration for development
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_TASK_ALWAYS_EAGER = True  # Execute tasks synchronously for development

# Security settings for development
SECRET_KEY = 'development-secret-key-change-in-production'
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True
